<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSH深度安全加固与防火墙协同防御实战</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            background: white;
            display: none;
            padding: 60px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: absolute;
            top: 0;
            left: 0;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        .slide h1 {
            color: #1e3c72;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #2a5298;
            padding-bottom: 15px;
        }

        .slide h2 {
            color: #2a5298;
            font-size: 2em;
            margin-bottom: 25px;
            border-left: 5px solid #1e3c72;
            padding-left: 20px;
        }

        .slide h3 {
            color: #1e3c72;
            font-size: 1.5em;
            margin-bottom: 20px;
            margin-top: 25px;
        }

        .slide p, .slide li {
            font-size: 1.2em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .slide ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .slide li {
            margin-bottom: 10px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 1em;
            margin: 20px 0;
            overflow-x: auto;
            border-left: 5px solid #4299e1;
        }

        .command {
            color: #68d391;
        }

        .output {
            color: #f7fafc;
            background: #1a202c;
        }

        .highlight {
            background: #fef5e7;
            padding: 15px;
            border-left: 5px solid #f6ad55;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning {
            background: #fed7d7;
            padding: 15px;
            border-left: 5px solid #fc8181;
            margin: 20px 0;
            border-radius: 5px;
        }

        .success {
            background: #c6f6d5;
            padding: 15px;
            border-left: 5px solid #68d391;
            margin: 20px 0;
            border-radius: 5px;
        }

        .diagram {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #2a5298;
            border-radius: 10px;
            background: #f7fafc;
        }

        /* 增强的导航控制区域 */
        .navigation-controls {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(180deg, transparent 0%, rgba(30, 60, 114, 0.95) 100%);
            padding: 20px;
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .nav-main {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 30px;
            margin-bottom: 15px;
        }

        .nav-btn {
            background: linear-gradient(45deg, #2a5298, #1e3c72);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(42, 82, 152, 0.3);
            min-width: 120px;
            position: relative;
            overflow: hidden;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .nav-btn:hover::before {
            left: 100%;
        }

        .nav-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 25px rgba(42, 82, 152, 0.4);
            background: linear-gradient(45deg, #3a6bb8, #2e4c92);
        }

        .nav-btn:active {
            transform: translateY(-1px) scale(1.02);
        }

        .nav-btn:disabled {
            background: linear-gradient(45deg, #a0aec0, #8b92a5);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 2px 8px rgba(160, 174, 192, 0.2);
        }

        .nav-btn:disabled::before {
            display: none;
        }

        /* 快速跳转按钮 */
        .quick-nav {
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s;
            backdrop-filter: blur(5px);
        }

        .quick-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .quick-btn.active {
            background: rgba(255, 215, 0, 0.3);
            border-color: rgba(255, 215, 0, 0.8);
            color: #ffd700;
        }

        /* 侧边导航按钮 */
        .side-nav {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .side-nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(42, 82, 152, 0.9);
            color: white;
            border: none;
            cursor: pointer;
            font-size: 1.5em;
            transition: all 0.3s;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(42, 82, 152, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .side-nav-btn:hover {
            transform: scale(1.1);
            background: rgba(30, 60, 114, 0.95);
            box-shadow: 0 6px 20px rgba(42, 82, 152, 0.5);
        }

        .slide-counter {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(30, 60, 114, 0.9);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 1em;
            z-index: 1000;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(30, 60, 114, 0.3);
            font-weight: bold;
        }

        /* 进度条 */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.1);
            z-index: 1001;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ffd700, #ff6b6b);
            width: 0%;
            transition: width 0.3s ease;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: calc(100% - 200px);
        }

        .three-column {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            height: calc(100% - 200px);
        }

        .network-diagram {
            font-family: monospace;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #dee2e6;
        }

        .step-number {
            background: #2a5298;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }

        .config-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        .config-section h4 {
            color: #1e3c72;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        /* 键盘提示 */
        .keyboard-hint {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9em;
            z-index: 1000;
            backdrop-filter: blur(10px);
            opacity: 0;
            transition: opacity 0.3s;
        }

        .keyboard-hint.show {
            opacity: 1;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navigation-controls {
                padding: 15px;
            }
            
            .nav-main {
                flex-direction: column;
                gap: 15px;
            }
            
            .nav-btn {
                width: 100%;
                max-width: 300px;
            }
            
            .side-nav {
                right: 10px;
            }
            
            .quick-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>

    <div class="keyboard-hint" id="keyboardHint">
        按 空格键 或 → 下一页 | ← 上一页 | F11 全屏
    </div>

    <div class="presentation-container">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">3</span>
        </div>

        <!-- 侧边导航 -->
        <div class="side-nav">
            <button class="side-nav-btn" onclick="goToSlide(0)" title="首页">🏠</button>
            <button class="side-nav-btn" onclick="changeSlide(-1)" title="上一页">◀</button>
            <button class="side-nav-btn" onclick="changeSlide(1)" title="下一页">▶</button>
            <button class="side-nav-btn" onclick="goToSlide(totalSlides-1)" title="末页">🏁</button>
            <button class="side-nav-btn" onclick="toggleFullscreen()" title="全屏">⛶</button>
        </div>

        <!-- 幻灯片 1: 标题页 -->
        <div class="slide active">
            <h1>SSH深度安全加固与防火墙协同防御实战</h1>
            <div style="text-align: center; margin-top: 50px;">
                <h2 style="border: none; color: #2a5298;">使用Kali Linux构建企业级SSH安全防护体系</h2>
                <div style="margin-top: 80px; font-size: 1.3em;">
                    <p><strong>讲师：</strong> 网络安全专家</p>
                    <p><strong>时长：</strong> 3小时（理论 + 演示 + 实验）</p>
                    <p><strong>目标受众：</strong> 具有Linux基础的网络安全初学者</p>
                </div>
                <div class="highlight" style="margin-top: 50px;">
                    <p><strong>学习目标：</strong> 掌握SSH服务深度加固技术，构建服务层与网络层的双重防御体系</p>
                </div>
                
                <div class="success" style="margin-top: 40px;">
                    <h3>🎯 导航说明</h3>
                    <p>使用以下方式浏览演示文稿：</p>
                    <ul style="text-align: left; margin-top: 20px;">
                        <li><strong>键盘：</strong> 空格键/箭头键 翻页，F11 全屏</li>
                        <li><strong>鼠标：</strong> 底部导航按钮，侧边快捷按钮</li>
                        <li><strong>触屏：</strong> 左右滑动手势</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 幻灯片 2: SSH协议基础 -->
        <div class="slide">
            <h1>SSH协议安全基础</h1>
            <div class="two-column">
                <div>
                    <h3>🔐 SSH核心特性</h3>
                    <ul>
                        <li><strong>远程安全访问：</strong> 替代Telnet等明文协议</li>
                        <li><strong>数据加密传输：</strong> 防止网络窃听</li>
                        <li><strong>身份认证：</strong> 确保连接双方身份</li>
                        <li><strong>数据完整性：</strong> 防止传输过程篡改</li>
                    </ul>
                    
                    <h3>🔑 加密算法组合</h3>
                    <ul>
                        <li><strong>非对称加密：</strong> RSA/ECDSA（密钥交换）</li>
                        <li><strong>对称加密：</strong> AES（数据传输）</li>
                        <li><strong>哈希算法：</strong> SHA-2（完整性校验）</li>
                    </ul>

                    <div class="warning">
                        <h4>🚨 默认配置风险</h4>
                        <ul>
                            <li>端口22易被扫描</li>
                            <li>密码认证易被暴力破解</li>
                            <li>Root直接登录风险</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div class="network-diagram">
                        <h4>SSH连接建立流程</h4>
                        <pre>
客户端                    服务器
  |                        |
  |--- 1. TCP连接建立 ----->|
  |<-- 2. 版本协商 ---------|
  |--- 3. 算法协商 -------->|
  |<-- 4. 服务器公钥 -------|
  |--- 5. 客户端认证 ------>|
  |<-- 6. 认证结果 ---------|
  |=== 7. 加密通道建立 ====|
                        </pre>
                    </div>
                    
                    <div class="code-block">
                        <span class="command"># 查看SSH配置风险项</span><br>
                        grep -E "^(Port|PermitRootLogin|PasswordAuthentication)" /etc/ssh/sshd_config
                    </div>
                    
                    <div class="code-block output">
                        Port 22                     # 🔴 默认端口
                        PermitRootLogin yes         # 🔴 Root登录  
                        PasswordAuthentication yes  # 🔴 密码认证
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 3: 防火墙协同防御 -->
        <div class="slide">
            <h1>SSH与防火墙协同防御体系</h1>
            <div class="diagram">
                <h3>🔄 多层防御架构设计</h3>
                <div class="network-diagram">
                    <pre>
                    攻击者
                        ↓
                ┌─────────────────┐
                │   网络层防护    │ ← UFW防火墙规则
                │  端口/IP过滤   │   (第一道防线)
                └─────────────────┘
                        ↓
                ┌─────────────────┐
                │   应用层防护    │ ← SSH服务配置
                │  认证/授权控制  │   (第二道防线)
                └─────────────────┘
                        ↓
                ┌─────────────────┐
                │   系统层防护    │ ← fail2ban/SELinux
                │  行为监控分析   │   (第三道防线)
                └─────────────────┘
                        ↓
                    受保护资源
                    </pre>
                </div>
            </div>
            
            <div class="three-column">
                <div class="success">
                    <h4>🔐 SSH层面防护</h4>
                    <ul>
                        <li>强制密钥认证</li>
                        <li>禁用密码登录</li>
                        <li>非标准端口</li>
                        <li>Root权限控制</li>
                        <li>会话管理</li>
                    </ul>
                </div>
                <div class="success">
                    <h4>🛡️ 网络层面防护</h4>
                    <ul>
                        <li>默认拒绝策略</li>
                        <li>IP白名单控制</li>
                        <li>端口访问限制</li>
                        <li>连接状态跟踪</li>
                        <li>流量监控记录</li>
                    </ul>
                </div>
                <div class="success">
                    <h4>🚨 行为层面防护</h4>
                    <ul>
                        <li>失败尝试检测</li>
                        <li>自动封禁机制</li>
                        <li>异常行为分析</li>
                        <li>实时告警响应</li>
                        <li>安全事件记录</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 增强的导航控制区域 -->
    <div class="navigation-controls">
        <div class="nav-main">
            <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">
                ◀ 上一页
            </button>
            <button class="nav-btn" onclick="goToSlide(0)">
                🏠 首页
            </button>
            <button class="nav-btn" onclick="toggleFullscreen()">
                ⛶ 全屏
            </button>
            <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">
                下一页 ▶
            </button>
        </div>
        
        <div class="quick-nav">
            <button class="quick-btn active" onclick="goToSlide(0)">标题页</button>
            <button class="quick-btn" onclick="goToSlide(1)">SSH基础</button>
            <button class="quick-btn" onclick="goToSlide(2)">协同防御</button>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;
        
        function updateProgress() {
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }
        
        function updateQuickNav() {
            const quickBtns = document.querySelectorAll('.quick-btn');
            quickBtns.forEach((btn, index) => {
                btn.classList.toggle('active', index === currentSlide);
            });
        }
        
        function showSlide(n) {
            if (n < 0 || n >= totalSlides) return;
            
            slides[currentSlide].classList.remove('active');
            currentSlide = n;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            
            // 更新导航按钮状态
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
            
            updateProgress();
            updateQuickNav();
        }
        
        function changeSlide(direction) {
            const newSlide = currentSlide + direction;
            if (newSlide >= 0 && newSlide < totalSlides) {
                showSlide(newSlide);
            }
        }
        
        function goToSlide(n) {
            showSlide(n);
        }
        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // 显示键盘提示
        function showKeyboardHint() {
            const hint = document.getElementById('keyboardHint');
            hint.classList.add('show');
            setTimeout(() => {
                hint.classList.remove('show');
            }, 3000);
        }
        
        // 键盘导航
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case 'ArrowRight':
                case ' ':
                case 'PageDown':
                    if (currentSlide < totalSlides - 1) {
                        changeSlide(1);
                    }
                    event.preventDefault();
                    break;
                case 'ArrowLeft':
                case 'PageUp':
                    if (currentSlide > 0) {
                        changeSlide(-1);
                    }
                    event.preventDefault();
                    break;
                case 'Home':
                    goToSlide(0);
                    event.preventDefault();
                    break;
                case 'End':
                    goToSlide(totalSlides - 1);
                    event.preventDefault();
                    break;
                case 'F11':
                    toggleFullscreen();
                    event.preventDefault();
                    break;
                case '?':
                case 'h':
                case 'H':
                    showKeyboardHint();
                    event.preventDefault();
                    break;
            }
        });
        
        // 鼠标滚轮导航
        document.addEventListener('wheel', function(event) {
            if (Math.abs(event.deltaY) > 50) {
                if (event.deltaY > 0 && currentSlide < totalSlides - 1) {
                    changeSlide(1);
                } else if (event.deltaY < 0 && currentSlide > 0) {
                    changeSlide(-1);
                }
                event.preventDefault();
            }
        });
        
        // 触摸滑动支持
        let startX = null;
        let startY = null;
        
        document.addEventListener('touchstart', function(event) {
            startX = event.touches.clientX;
            startY = event.touches.clientY;
        });
        
        document.addEventListener('touchend', function(event) {
            if (startX === null || startY === null) return;
            
            const endX = event.changedTouches.clientX;
            const endY = event.changedTouches.clientY;
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0 && currentSlide > 0) {
                    changeSlide(-1);
                } else if (deltaX < 0 && currentSlide < totalSlides - 1) {
                    changeSlide(1);
                }
            }
            
            startX = null;
            startY = null;
        });
        
        // 初始化
        showSlide(0);
        
        // 页面加载时显示键盘提示
        window.addEventListener('load', function() {
            setTimeout(showKeyboardHint, 1000);
        });
    </script>
</body>
</html>
