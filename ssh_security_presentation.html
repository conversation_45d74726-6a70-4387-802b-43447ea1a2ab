<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SSH深度安全加固与防火墙协同防御实战</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            background: white;
            display: none;
            padding: 60px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: absolute;
            top: 0;
            left: 0;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        .slide h1 {
            color: #1e3c72;
            font-size: 2.5em;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 3px solid #2a5298;
            padding-bottom: 15px;
        }

        .slide h2 {
            color: #2a5298;
            font-size: 2em;
            margin-bottom: 25px;
            border-left: 5px solid #1e3c72;
            padding-left: 20px;
        }

        .slide h3 {
            color: #1e3c72;
            font-size: 1.5em;
            margin-bottom: 20px;
            margin-top: 25px;
        }

        .slide p, .slide li {
            font-size: 1.2em;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .slide ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .slide li {
            margin-bottom: 10px;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 1em;
            margin: 20px 0;
            overflow-x: auto;
            border-left: 5px solid #4299e1;
        }

        .command {
            color: #68d391;
        }

        .output {
            color: #f7fafc;
            background: #1a202c;
        }

        .highlight {
            background: #fef5e7;
            padding: 15px;
            border-left: 5px solid #f6ad55;
            margin: 20px 0;
            border-radius: 5px;
        }

        .warning {
            background: #fed7d7;
            padding: 15px;
            border-left: 5px solid #fc8181;
            margin: 20px 0;
            border-radius: 5px;
        }

        .success {
            background: #c6f6d5;
            padding: 15px;
            border-left: 5px solid #68d391;
            margin: 20px 0;
            border-radius: 5px;
        }

        .diagram {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #2a5298;
            border-radius: 10px;
            background: #f7fafc;
        }

        .navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }

        .nav-btn {
            background: #2a5298;
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            background: #1e3c72;
            transform: translateY(-2px);
        }

        .nav-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(30, 60, 114, 0.9);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1em;
            z-index: 1000;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            height: calc(100% - 200px);
        }

        .three-column {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            height: calc(100% - 200px);
        }

        .network-diagram {
            font-family: monospace;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #dee2e6;
        }

        .step-number {
            background: #2a5298;
            color: white;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-weight: bold;
        }

        .config-section {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        .config-section h4 {
            color: #1e3c72;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="slide-counter">
            <span id="current-slide">1</span> / <span id="total-slides">25</span>
        </div>

        <!-- 幻灯片 1: 标题页 -->
        <div class="slide active">
            <h1>SSH深度安全加固与防火墙协同防御实战</h1>
            <div style="text-align: center; margin-top: 50px;">
                <h2 style="border: none; color: #2a5298;">使用Kali Linux构建企业级SSH安全防护体系</h2>
                <div style="margin-top: 80px; font-size: 1.3em;">
                    <p><strong>讲师：</strong> 网络安全专家</p>
                    <p><strong>时长：</strong> 3小时（理论 + 演示 + 实验）</p>
                    <p><strong>目标受众：</strong> 具有Linux基础的网络安全初学者</p>
                </div>
                <div class="highlight" style="margin-top: 50px;">
                    <p><strong>学习目标：</strong> 掌握SSH服务深度加固技术，构建服务层与网络层的双重防御体系</p>
                </div>
            </div>
        </div>

        <!-- 幻灯片 2: 课程大纲 -->
        <div class="slide">
            <h1>课程大纲</h1>
            <div class="three-column">
                <div class="config-section">
                    <h4>📚 第一部分：理论基础</h4>
                    <ul>
                        <li>SSH协议原理回顾</li>
                        <li>SSH核心风险分析</li>
                        <li>防火墙基础概念</li>
                        <li>协同防御思想</li>
                    </ul>
                    <p><strong>时间：45分钟</strong></p>
                </div>
                <div class="config-section">
                    <h4>👨💻 第二部分：讲师演示</h4>
                    <ul>
                        <li>实验环境准备</li>
                        <li>SSH深度加固实操</li>
                        <li>UFW防火墙配置</li>
                        <li>安全测试验证</li>
                    </ul>
                    <p><strong>时间：45分钟</strong></p>
                </div>
                <div class="config-section">
                    <h4>🔬 第三部分：学员实验</h4>
                    <ul>
                        <li>SSH加固复现</li>
                        <li>防火墙配置实践</li>
                        <li>攻击模拟验证</li>
                        <li>故障排除练习</li>
                    </ul>
                    <p><strong>时间：75分钟</strong></p>
                </div>
            </div>
            <div class="success">
                <p><strong>实验环境要求：</strong> 两台虚拟机（Kali Linux + Debian/Ubuntu），网络连通</p>
            </div>
        </div>

        <!-- 幻灯片 3: SSH协议基础回顾 -->
        <div class="slide">
            <h1>第一部分：SSH协议基础回顾</h1>
            <div class="two-column">
                <div>
                    <h3>🔐 SSH核心特性</h3>
                    <ul>
                        <li><strong>远程安全访问：</strong> 替代Telnet等明文协议</li>
                        <li><strong>数据加密传输：</strong> 防止网络窃听</li>
                        <li><strong>身份认证：</strong> 确保连接双方身份</li>
                        <li><strong>数据完整性：</strong> 防止传输过程篡改</li>
                    </ul>
                    
                    <h3>🔑 加密算法组合</h3>
                    <ul>
                        <li><strong>非对称加密：</strong> RSA/ECDSA（密钥交换）</li>
                        <li><strong>对称加密：</strong> AES（数据传输）</li>
                        <li><strong>哈希算法：</strong> SHA-2（完整性校验）</li>
                    </ul>
                </div>
                <div>
                    <div class="network-diagram">
                        <h4>SSH连接建立流程</h4>
                        <pre>
客户端                    服务器
  |                        |
  |--- 1. TCP连接建立 ----->|
  |<-- 2. 版本协商 ---------|
  |--- 3. 算法协商 -------->|
  |<-- 4. 服务器公钥 -------|
  |--- 5. 客户端认证 ------>|
  |<-- 6. 认证结果 ---------|
  |=== 7. 加密通道建立 ====|
                        </pre>
                    </div>
                    
                    <div class="highlight">
                        <p><strong>默认认证方式：</strong></p>
                        <ul>
                            <li>密码认证（安全性较低）</li>
                            <li>公钥认证（推荐使用）</li>
                            <li>双因子认证（最高安全级别）</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 4: SSH核心风险分析 -->
        <div class="slide">
            <h1>SSH核心风险深度分析</h1>
            <div class="two-column">
                <div>
                    <h3>🚨 主要安全威胁</h3>
                    
                    <div class="warning">
                        <h4>1. 暴力破解攻击</h4>
                        <ul>
                            <li>弱密码字典攻击</li>
                            <li>常见用户名枚举</li>
                            <li>分布式破解尝试</li>
                        </ul>
                    </div>
                    
                    <div class="warning">
                        <h4>2. 中间人攻击</h4>
                        <ul>
                            <li>首次连接主机密钥欺骗</li>
                            <li>DNS劫持配合攻击</li>
                            <li>证书替换攻击</li>
                        </ul>
                    </div>
                    
                    <div class="warning">
                        <h4>3. 权限滥用风险</h4>
                        <ul>
                            <li>Root用户直接登录</li>
                            <li>共享账户使用</li>
                            <li>权限提升漏洞利用</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <h3>📊 默认配置风险评估</h3>
                    
                    <div class="code-block">
                        <span class="command"># 查看默认SSH配置中的风险项</span><br>
                        grep -E "^(Port|PermitRootLogin|PasswordAuthentication|PubkeyAuthentication)" /etc/ssh/sshd_config
                    </div>
                    
                    <div class="code-block output">
                        Port 22                     # 🔴 默认端口易被扫描
                        PermitRootLogin yes         # 🔴 允许root直接登录  
                        PasswordAuthentication yes  # 🔴 允许密码认证
                        PubkeyAuthentication yes    # 🟢 支持公钥认证
                    </div>
                    
                    <div class="highlight">
                        <h4>🎯 常见攻击场景模拟</h4>
                        <div class="code-block">
                            <span class="command"># 攻击者端口扫描</span><br>
                            nmap -p 22 ***********/24<br><br>
                            <span class="command"># 暴力破解尝试</span><br>
                            hydra -l root -P passwords.txt ssh://*************<br><br>
                            <span class="command"># 用户名枚举</span><br>
                            ssh-audit *************
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 5: 防火墙基础概念 -->
        <div class="slide">
            <h1>Linux防火墙基础与UFW详解</h1>
            <div class="two-column">
                <div>
                    <h3>🛡️ 防火墙核心概念</h3>
                    
                    <div class="config-section">
                        <h4>工作原理</h4>
                        <ul>
                            <li><strong>包过滤：</strong> 基于IP、端口、协议过滤</li>
                            <li><strong>状态检测：</strong> 跟踪连接状态</li>
                            <li><strong>应用层检测：</strong> 深度包检测</li>
                        </ul>
                    </div>
                    
                    <div class="config-section">
                        <h4>UFW vs iptables</h4>
                        <ul>
                            <li><strong>UFW：</strong> 用户友好的前端工具</li>
                            <li><strong>iptables：</strong> 底层内核接口</li>
                            <li><strong>关系：</strong> UFW → iptables → netfilter</li>
                        </ul>
                    </div>
                    
                    <h3>📋 防火墙策略类型</h3>
                    <ul>
                        <li><strong>默认拒绝：</strong> 白名单模式（推荐）</li>
                        <li><strong>默认允许：</strong> 黑名单模式（不推荐）</li>
                        <li><strong>混合策略：</strong> 分层防护</li>
                    </ul>
                </div>
                <div>
                    <div class="network-diagram">
                        <h4>网络防护层次结构</h4>
                        <pre>
    互联网
        ↓
  [边界防火墙]
        ↓
    内部网络
        ↓
  [主机防火墙] ← UFW
        ↓
   [SSH服务] ← sshd配置
        ↓
    操作系统
                        </pre>
                    </div>
                    
                    <div class="code-block">
                        <span class="command"># UFW基本操作命令</span><br>
                        sudo ufw status                    # 查看状态<br>
                        sudo ufw enable                    # 启用防火墙<br>
                        sudo ufw default deny incoming     # 默认拒绝入站<br>
                        sudo ufw default allow outgoing    # 默认允许出站<br>
                        sudo ufw allow 22/tcp             # 允许SSH<br>
                        sudo ufw delete allow 22          # 删除规则<br>
                        sudo ufw reset                     # 重置所有规则
                    </div>
                    
                    <div class="success">
                        <p><strong>最佳实践：</strong> 先配置允许规则，再启用防火墙，避免锁定自己</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 6: 协同防御思想 -->
        <div class="slide">
            <h1>SSH与防火墙协同防御体系</h1>
            <div class="diagram">
                <h3>🔄 多层防御架构设计</h3>
                <div class="network-diagram">
                    <pre>
                    攻击者
                        ↓
                ┌─────────────────┐
                │   网络层防护    │ ← UFW防火墙规则
                │  端口/IP过滤   │   (第一道防线)
                └─────────────────┘
                        ↓
                ┌─────────────────┐
                │   应用层防护    │ ← SSH服务配置
                │  认证/授权控制  │   (第二道防线)
                └─────────────────┘
                        ↓
                ┌─────────────────┐
                │   系统层防护    │ ← fail2ban/SELinux
                │  行为监控分析   │   (第三道防线)
                └─────────────────┘
                        ↓
                    受保护资源
                    </pre>
                </div>
            </div>
            
            <div class="two-column">
                <div>
                    <h3>🎯 防御策略组合</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">1</span>网络边界控制</h4>
                        <ul>
                            <li>IP白名单访问控制</li>
                            <li>非标准端口使用</li>
                            <li>端口敲门技术</li>
                        </ul>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">2</span>服务层面加固</h4>
                        <ul>
                            <li>密钥认证强制</li>
                            <li>Root登录禁用</li>
                            <li>会话超时控制</li>
                        </ul>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">3</span>行为监控防护</h4>
                        <ul>
                            <li>失败尝试检测</li>
                            <li>异常行为分析</li>
                            <li>自动响应机制</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <h3>⚡ 协同效应分析</h3>
                    
                    <div class="highlight">
                        <h4>单一防护的局限性</h4>
                        <ul>
                            <li><strong>仅SSH配置：</strong> 仍暴露攻击面</li>
                            <li><strong>仅防火墙：</strong> 应用层攻击无效</li>
                            <li><strong>缺乏监控：</strong> 攻击行为难以发现</li>
                        </ul>
                    </div>
                    
                    <div class="success">
                        <h4>协同防御优势</h4>
                        <ul>
                            <li><strong>纵深防御：</strong> 多层安全保障</li>
                            <li><strong>互补增强：</strong> 弥补单点缺陷</li>
                            <li><strong>动态响应：</strong> 实时威胁处理</li>
                            <li><strong>可见可控：</strong> 全面安全态势</li>
                        </ul>
                    </div>
                    
                    <div class="code-block">
                        <span class="command"># 查看当前安全状态</span><br>
                        sudo ufw status verbose            # 防火墙状态<br>
                        sudo systemctl status ssh         # SSH服务状态<br>
                        sudo fail2ban-client status       # 入侵检测状态<br>
                        sudo last | head -10              # 登录历史
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 7: 第二部分开始 - 实验环境准备 -->
        <div class="slide">
            <h1>第二部分：讲师演示 - 实验环境准备</h1>
            <div class="two-column">
                <div>
                    <h3>🖥️ 虚拟机环境配置</h3>
                    
                    <div class="config-section">
                        <h4>攻击/管理端：Kali Linux</h4>
                        <ul>
                            <li><strong>IP地址：</strong> *************</li>
                            <li><strong>用途：</strong> SSH客户端、安全测试</li>
                            <li><strong>必需工具：</strong> nmap, hydra, ssh-audit</li>
                        </ul>
                        
                        <div class="code-block">
                            <span class="command"># Kali环境验证</span><br>
                            ip addr show                   # 查看IP配置<br>
                            which nmap hydra ssh-audit     # 验证工具存在<br>
                            ssh-keygen -t rsa -b 4096      # 生成SSH密钥对
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>目标服务器：Ubuntu/Debian</h4>
                        <ul>
                            <li><strong>IP地址：</strong> *************</li>
                            <li><strong>用途：</strong> SSH服务器、加固目标</li>
                            <li><strong>初始状态：</strong> 默认SSH配置</li>
                        </ul>
                        
                        <div class="code-block">
                            <span class="command"># 服务器环境验证</span><br>
                            sudo systemctl status ssh      # SSH服务状态<br>
                            sudo ufw status                # 防火墙状态<br>
                            netstat -tlnp | grep :22      # SSH端口监听
                        </div>
                    </div>
                </div>
                <div>
                    <div class="network-diagram">
                        <h4>实验网络拓扑</h4>
                        <pre>
        ┌─────────────────┐
        │   Kali Linux    │
        │  *************  │ ← 攻击/管理端
        │   SSH Client    │
        └─────────┬───────┘
                  │
            网络交换机
            (***********/24)
                  │
        ┌─────────┴───────┐
        │ Ubuntu Server   │
        │ *************   │ ← 目标服务器
        │   SSH Server    │
        └─────────────────┘
                        </pre>
                    </div>
                    
                    <div class="warning">
                        <h4>⚠️ 实验前注意事项</h4>
                        <ul>
                            <li>确保两台虚拟机网络连通</li>
                            <li>备份原始SSH配置文件</li>
                            <li>准备应急访问方式（控制台）</li>
                            <li>记录初始网络配置</li>
                        </ul>
                    </div>
                    
                    <div class="code-block">
                        <span class="command"># 网络连通性测试</span><br>
                        ping -c 3 *************        # 从Kali测试连通性<br>
                        ssh user@*************         # 测试SSH初始连接<br>
                        cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 8: SSH密钥对配置 -->
        <div class="slide">
            <h1>SSH密钥对生成与部署</h1>
            <div class="two-column">
                <div>
                    <h3>🔑 Step 1: 在Kali上生成密钥对</h3>
                    
                    <div class="code-block">
                        <span class="command"># 生成RSA密钥对（推荐4096位）</span><br>
                        ssh-keygen -t rsa -b 4096 -C "admin@kali-system"<br><br>
                        <span class="command"># 或生成更安全的ED25519密钥</span><br>
                        ssh-keygen -t ed25519 -C "admin@kali-system"
                    </div>
                    
                    <div class="code-block output">
                        Generating public/private rsa key pair.
                        Enter file in which to save the key (/home/<USER>/.ssh/id_rsa): 
                        Enter passphrase (empty for no passphrase): 
                        Enter same passphrase again: 
                        Your identification has been saved in /home/<USER>/.ssh/id_rsa
                        Your public key has been saved in /home/<USER>/.ssh/id_rsa.pub
                    </div>
                    
                    <div class="highlight">
                        <h4>🔐 密钥安全建议</h4>
                        <ul>
                            <li>设置强密码短语保护私钥</li>
                            <li>私钥权限设置为600</li>
                            <li>定期轮换密钥对</li>
                            <li>备份密钥到安全位置</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <h3>📤 Step 2: 部署公钥到服务器</h3>
                    
                    <div class="code-block">
                        <span class="command"># 方法1：使用ssh-copy-id（推荐）</span><br>
                        ssh-copy-id user@*************<br><br>
                        <span class="command"># 方法2：手动复制</span><br>
                        cat ~/.ssh/id_rsa.pub | ssh user@************* \<br>
                        "mkdir -p ~/.ssh && cat >> ~/.ssh/authorized_keys"
                    </div>
                    
                    <div class="code-block output">
                        /usr/bin/ssh-copy-id: INFO: Source of key(s) to be installed: "/home/<USER>/.ssh/id_rsa.pub"
                        Number of key(s) added: 1

                        Now try logging into the machine, with:   "ssh 'user@*************'"
                        and check to make sure that only the key(s) you wanted were added.
                    </div>
                    
                    <h3>✅ Step 3: 验证密钥认证</h3>
                    
                    <div class="code-block">
                        <span class="command"># 测试密钥登录</span><br>
                        ssh user@*************<br><br>
                        <span class="command"># 在服务器上验证authorized_keys</span><br>
                        ls -la ~/.ssh/<br>
                        cat ~/.ssh/authorized_keys
                    </div>
                    
                    <div class="success">
                        <p><strong>成功标志：</strong> 无需输入密码即可登录服务器</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 9: SSH服务深度加固 -->
        <div class="slide">
            <h1>SSH服务配置深度加固</h1>
            <div class="two-column">
                <div>
                    <h3>⚙️ 关键配置项修改</h3>
                    
                    <div class="code-block">
                        <span class="command"># 编辑SSH服务配置</span><br>
                        sudo nano /etc/ssh/sshd_config
                    </div>
                    
                    <div class="config-section">
                        <h4>基础安全配置</h4>
                        <div class="code-block">
                            # 更改默认端口（避免扫描）<br>
                            Port 2222<br><br>
                            # 禁用Root用户登录<br>
                            PermitRootLogin no<br><br>
                            # 强制密钥认证，禁用密码<br>
                            PasswordAuthentication no<br>
                            PubkeyAuthentication yes<br>
                            AuthorizedKeysFile .ssh/authorized_keys<br><br>
                            # 禁用空密码账户<br>
                            PermitEmptyPasswords no
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>高级安全选项</h4>
                        <div class="code-block">
                            # 限制登录用户<br>
                            AllowUsers user admin<br><br>
                            # 设置会话超时<br>
                            ClientAliveInterval 300<br>
                            ClientAliveCountMax 2<br><br>
                            # 限制并发连接数<br>
                            MaxAuthTries 3<br>
                            MaxSessions 2
                        </div>
                    </div>
                </div>
                <div>
                    <h3>🔒 协议安全强化</h3>
                    
                    <div class="code-block">
                        # 仅使用SSH协议版本2<br>
                        Protocol 2<br><br>
                        # 强化加密算法<br>
                        Ciphers <EMAIL>,<EMAIL><br>
                        MACs hmac-sha2-256,hmac-sha2-512<br>
                        KexAlgorithms <EMAIL><br><br>
                        # 禁用危险功能<br>
                        X11Forwarding no<br>
                        AllowTcpForwarding no<br>
                        GatewayPorts no<br>
                        PermitTunnel no
                    </div>
                    
                    <h3>🔄 应用配置更改</h3>
                    
                    <div class="code-block">
                        <span class="command"># 验证配置语法</span><br>
                        sudo sshd -t<br><br>
                        <span class="command"># 重启SSH服务</span><br>
                        sudo systemctl restart ssh<br><br>
                        <span class="command"># 确认服务状态</span><br>
                        sudo systemctl status ssh<br><br>
                        <span class="command"># 验证新端口监听</span><br>
                        sudo netstat -tlnp | grep :2222
                    </div>
                    
                    <div class="warning">
                        <p><strong>⚠️ 重要提醒：</strong> 配置更改前确保有其他访问方式，避免锁定系统</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 10: fail2ban配置 -->
        <div class="slide">
            <h1>fail2ban入侵检测与自动防护</h1>
            <div class="two-column">
                <div>
                    <h3>📦 安装与基础配置</h3>
                    
                    <div class="code-block">
                        <span class="command"># 安装fail2ban</span><br>
                        sudo apt update<br>
                        sudo apt install fail2ban -y<br><br>
                        <span class="command"># 创建本地配置文件</span><br>
                        sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
                    </div>
                    
                    <div class="config-section">
                        <h4>SSH保护配置</h4>
                        <div class="code-block">
                            <span class="command"># 编辑SSH jail配置</span><br>
                            sudo nano /etc/fail2ban/jail.local
                        </div>
                        
                        <div class="code-block">
                            [sshd]<br>
                            enabled = true<br>
                            port = 2222<br>
                            filter = sshd<br>
                            logpath = /var/log/auth.log<br>
                            maxretry = 3<br>
                            bantime = 3600<br>
                            findtime = 600
                        </div>
                    </div>
                    
                    <div class="highlight">
                        <h4>配置参数说明</h4>
                        <ul>
                            <li><strong>maxretry:</strong> 最大失败次数</li>
                            <li><strong>bantime:</strong> 封禁时长（秒）</li>
                            <li><strong>findtime:</strong> 检测时间窗口</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <h3>🚀 启动与管理</h3>
                    
                    <div class="code-block">
                        <span class="command"># 启动fail2ban服务</span><br>
                        sudo systemctl start fail2ban<br>
                        sudo systemctl enable fail2ban<br><br>
                        <span class="command"># 查看服务状态</span><br>
                        sudo systemctl status fail2ban<br><br>
                        <span class="command"># 查看jail状态</span><br>
                        sudo fail2ban-client status<br>
                        sudo fail2ban-client status sshd
                    </div>
                    
                    <div class="code-block output">
                        Status for the jail: sshd
                        |- Filter
                        |  |- Currently failed: 0
                        |  |- Total failed:     0
                        |  `- Journal matches:  _SYSTEMD_UNIT=ssh.service + _COMM=sshd
                        `- Actions
                           |- Currently banned: 0
                           |- Total banned:     0
                           `- Banned IP list:
                    </div>
                    
                    <h3>🔍 监控与管理命令</h3>
                    
                    <div class="code-block">
                        <span class="command"># 查看被封禁的IP</span><br>
                        sudo fail2ban-client get sshd banip<br><br>
                        <span class="command"># 手动解封IP</span><br>
                        sudo fail2ban-client set sshd unbanip *************<br><br>
                        <span class="command"># 查看日志</span><br>
                        sudo tail -f /var/log/fail2ban.log
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 11: UFW防火墙配置实操 -->
        <div class="slide">
            <h1>UFW防火墙配置实操演示</h1>
            <div class="two-column">
                <div>
                    <h3>🛡️ 基础防火墙设置</h3>
                    
                    <div class="code-block">
                        <span class="command"># 重置防火墙规则（可选）</span><br>
                        sudo ufw --force reset<br><br>
                        <span class="command"># 设置默认策略</span><br>
                        sudo ufw default deny incoming<br>
                        sudo ufw default allow outgoing<br><br>
                        <span class="command"># 允许新SSH端口</span><br>
                        sudo ufw allow 2222/tcp<br><br>
                        <span class="command"># 启用防火墙</span><br>
                        sudo ufw enable
                    </div>
                    
                    <div class="code-block output">
                        Default incoming policy changed to 'deny'
                        Default outgoing policy changed to 'allow'
                        Rules updated
                        Firewall is active and enabled on system startup
                    </div>
                    
                    <h3>🎯 精确访问控制</h3>
                    
                    <div class="code-block">
                        <span class="command"># 限制SSH访问到特定IP</span><br>
                        sudo ufw delete allow 2222/tcp<br>
                        sudo ufw allow from ************* to any port 2222<br><br>
                        <span class="command"># 允许本地网段SSH（可选）</span><br>
                        sudo ufw allow from ***********/24 to any port 2222
                    </div>
                </div>
                <div>
                    <h3>📊 防火墙状态查看</h3>
                    
                    <div class="code-block">
                        <span class="command"># 查看详细状态</span><br>
                        sudo ufw status verbose
                    </div>
                    
                    <div class="code-block output">
                        Status: active
                        Logging: on (low)
                        Default: deny (incoming), allow (outgoing), disabled (routed)
                        New profiles: skip

                        To                         Action      From
                        --                         ------      ----
                        2222/tcp                   ALLOW IN    *************
                        2222/tcp                   ALLOW IN    ***********/24
                    </div>
                    
                    <h3>🔧 高级规则管理</h3>
                    
                    <div class="code-block">
                        <span class="command"># 查看规则编号</span><br>
                        sudo ufw status numbered<br><br>
                        <span class="command"># 删除指定规则</span><br>
                        sudo ufw delete 1<br><br>
                        <span class="command"># 插入规则到指定位置</span><br>
                        sudo ufw insert 1 allow from ************* to any port 2222<br><br>
                        <span class="command"># 查看防火墙日志</span><br>
                        sudo tail -f /var/log/ufw.log
                    </div>
                    
                    <div class="success">
                        <p><strong>配置完成标志：</strong> 只有指定IP可以通过新端口访问SSH</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 12: 安全测试验证 -->
        <div class="slide">
            <h1>安全配置测试与验证</h1>
            <div class="two-column">
                <div>
                    <h3>✅ 正向功能测试</h3>
                    
                    <div class="config-section">
                        <h4>1. 密钥认证测试</h4>
                        <div class="code-block">
                            <span class="command"># 从Kali连接到服务器</span><br>
                            ssh -p 2222 user@*************<br><br>
                            <span class="command"># 应该无需密码直接登录</span><br>
                            # 验证当前连接信息<br>
                            who am i<br>
                            echo $SSH_CONNECTION
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>2. 防火墙规则验证</h4>
                        <div class="code-block">
                            <span class="command"># 从允许的IP测试连接</span><br>
                            telnet ************* 2222<br><br>
                            <span class="command"># 端口开放性测试</span><br>
                            nmap -p 2222 *************
                        </div>
                        
                        <div class="code-block output">
                            Starting Nmap scan...
                            PORT     STATE SERVICE
                            2222/tcp open  ssh
                        </div>
                    </div>
                </div>
                <div>
                    <h3>🚫 安全防护测试</h3>
                    
                    <div class="config-section">
                        <h4>3. 密码认证禁用测试</h4>
                        <div class="code-block">
                            <span class="command"># 尝试强制密码认证</span><br>
                            ssh -p 2222 -o PreferredAuthentications=password user@*************
                        </div>
                        
                        <div class="code-block output">
                            user@*************: Permission denied (publickey).
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>4. 默认端口关闭验证</h4>
                        <div class="code-block">
                            <span class="command"># 扫描原22端口</span><br>
                            nmap -p 22 *************
                        </div>
                        
                        <div class="code-block output">
                            PORT   STATE    SERVICE
                            22/tcp filtered ssh
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>5. fail2ban功能测试</h4>
                        <div class="code-block">
                            <span class="command"># 模拟暴力破解（从其他IP）</span><br>
                            # 故意输入错误密码多次<br>
                            ssh -p 2222 fakeuser@*************<br><br>
                            <span class="command"># 查看fail2ban状态</span><br>
                            sudo fail2ban-client status sshd
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 13: 第三部分开始 - 学员实验指南 -->
        <div class="slide">
            <h1>第三部分：学员动手实验</h1>
            <div class="config-section">
                <h2>🎯 实验目标与环境要求</h2>
                <div class="two-column">
                    <div>
                        <h3>学习目标</h3>
                        <ul>
                            <li>独立完成SSH服务全面加固</li>
                            <li>掌握UFW防火墙配置技能</li>
                            <li>理解协同防御实施方法</li>
                            <li>具备安全测试验证能力</li>
                        </ul>
                        
                        <h3>预期成果</h3>
                        <ul>
                            <li>SSH服务安全基线达标</li>
                            <li>防火墙策略正确生效</li>
                            <li>安全威胁有效阻断</li>
                            <li>系统访问完全可控</li>
                        </ul>
                    </div>
                    <div>
                        <h3>环境准备清单</h3>
                        <div class="code-block">
                            <span class="command"># 检查网络连通性</span><br>
                            ping -c 3 *************<br><br>
                            <span class="command"># 验证SSH初始访问</span><br>
                            ssh user@*************<br><br>
                            <span class="command"># 检查必需工具</span><br>
                            which ssh-keygen nmap fail2ban ufw<br><br>
                            <span class="command"># 备份配置文件</span><br>
                            sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup
                        </div>
                        
                        <div class="warning">
                            <h4>⚠️ 安全提醒</h4>
                            <ul>
                                <li>确保虚拟机控制台访问可用</li>
                                <li>配置前备份重要文件</li>
                                <li>分步骤验证每个配置</li>
                                <li>遇到问题及时求助</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 14: 任务一 - SSH加固复现 -->
        <div class="slide">
            <h1>任务一：SSH深度加固实操 (25分钟)</h1>
            <div class="two-column">
                <div>
                    <h3>🔧 Step 1: 密钥对配置</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">1</span>生成SSH密钥对</h4>
                        <div class="code-block">
                            ssh-keygen -t rsa -b 4096 -C "<EMAIL>"
                        </div>
                        <p><strong>提示：</strong> 建议设置密码短语保护私钥</p>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">2</span>部署公钥到服务器</h4>
                        <div class="code-block">
                            ssh-copy-id user@*************
                        </div>
                        <p><strong>验证：</strong> 确认可以免密码登录</p>
                    </div>
                    
                    <h3>⚙️ Step 2: SSH服务配置</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">3</span>修改sshd_config</h4>
                        <div class="code-block">
                            sudo nano /etc/ssh/sshd_config
                        </div>
                        
                        <div class="highlight">
                            <h4>必须修改的配置项：</h4>
                            <ul>
                                <li>Port 2222</li>
                                <li>PermitRootLogin no</li>
                                <li>PasswordAuthentication no</li>
                                <li>MaxAuthTries 3</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div>
                    <h3>🔄 Step 3: 应用配置</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">4</span>验证配置语法</h4>
                        <div class="code-block">
                            sudo sshd -t
                        </div>
                        <p><strong>期望输出：</strong> 无错误信息</p>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">5</span>重启SSH服务</h4>
                        <div class="code-block">
                            sudo systemctl restart ssh<br>
                            sudo systemctl status ssh
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">6</span>验证新端口</h4>
                        <div class="code-block">
                            sudo netstat -tlnp | grep :2222<br>
                            ssh -p 2222 user@*************
                        </div>
                    </div>
                    
                    <div class="success">
                        <h4>✅ 任务一完成标准</h4>
                        <ul>
                            <li>SSH服务在2222端口正常运行</li>
                            <li>密钥认证工作正常</li>
                            <li>密码认证已禁用</li>
                            <li>Root登录已禁止</li>
                        </ul>
                    </div>
                    
                    <div class="warning">
                        <p><strong>故障排除：</strong> 如果无法连接，请检查防火墙规则和SSH配置语法</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 15: 任务二 - 防火墙配置 -->
        <div class="slide">
            <h1>任务二：UFW防火墙配置实践 (20分钟)</h1>
            <div class="two-column">
                <div>
                    <h3>🛡️ Step 1: 基础防火墙设置</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">1</span>设置默认策略</h4>
                        <div class="code-block">
                            sudo ufw default deny incoming<br>
                            sudo ufw default allow outgoing
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">2</span>开放SSH端口</h4>
                        <div class="code-block">
                            sudo ufw allow 2222/tcp
                        </div>
                        <p><strong>注意：</strong> 确保SSH端口允许后再启用防火墙</p>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">3</span>启用防火墙</h4>
                        <div class="code-block">
                            sudo ufw enable<br>
                            sudo ufw status verbose
                        </div>
                    </div>
                    
                    <h3>🎯 Step 2: 精确访问控制</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">4</span>限制SSH访问源</h4>
                        <div class="code-block">
                            sudo ufw delete allow 2222/tcp<br>
                            sudo ufw allow from ************* to any port 2222
                        </div>
                        <p><strong>说明：</strong> 只允许Kali机器访问SSH</p>
                    </div>
                </div>
                <div>
                    <h3>📊 Step 3: 验证与监控</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">5</span>验证规则生效</h4>
                        <div class="code-block">
                            sudo ufw status numbered<br>
                            sudo iptables -L -n
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">6</span>测试访问控制</h4>
                        <div class="code-block">
                            <span class="command"># 从Kali测试（应该成功）</span><br>
                            ssh -p 2222 user@*************<br><br>
                            <span class="command"># 从其他IP测试（应该失败）</span><br>
                            telnet ************* 2222
                        </div>
                    </div>
                    
                    <div class="highlight">
                        <h4>🔍 额外配置（可选）</h4>
                        <div class="code-block">
                            <span class="command"># 启用日志记录</span><br>
                            sudo ufw logging on<br><br>
                            <span class="command"># 查看被阻止的连接</span><br>
                            sudo tail -f /var/log/ufw.log<br><br>
                            <span class="command"># 允许本地回环</span><br>
                            sudo ufw allow in on lo<br>
                            sudo ufw allow out on lo
                        </div>
                    </div>
                    
                    <div class="success">
                        <h4>✅ 任务二完成标准</h4>
                        <ul>
                            <li>防火墙已启用并配置正确</li>
                            <li>只有指定IP可访问SSH</li>
                            <li>其他连接被正确阻断</li>
                            <li>日志记录功能正常</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 16: 任务三 - 攻击模拟验证 -->
        <div class="slide">
            <h1>任务三：攻击模拟与安全验证 (30分钟)</h1>
            <div class="two-column">
                <div>
                    <h3>🎯 测试场景一：端口扫描检测</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">1</span>全端口扫描测试</h4>
                        <div class="code-block">
                            <span class="command"># 从Kali执行端口扫描</span><br>
                            nmap -p 1-65535 *************<br><br>
                            <span class="command"># 针对性SSH端口扫描</span><br>
                            nmap -p 22,2222 -sS *************
                        </div>
                        
                        <div class="code-block output">
                            PORT     STATE    SERVICE
                            22/tcp   filtered ssh
                            2222/tcp open     ssh
                        </div>
                        
                        <p><strong>预期结果：</strong> 22端口显示filtered，2222端口显示open</p>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">2</span>服务指纹识别</h4>
                        <div class="code-block">
                            nmap -sV -p 2222 *************<br>
                            ssh-audit *************:2222
                        </div>
                    </div>
                    
                    <h3>🚫 测试场景二：认证绕过尝试</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">3</span>密码认证测试</h4>
                        <div class="code-block">
                            ssh -p 2222 -o PreferredAuthentications=password user@*************
                        </div>
                        <p><strong>预期结果：</strong> Permission denied (publickey)</p>
                    </div>
                </div>
                <div>
                    <h3>⚡ 测试场景三：fail2ban验证</h3>
                    
                    <div class="config-section">
                        <h4><span class="step-number">4</span>安装fail2ban</h4>
                        <div class="code-block">
                            sudo apt install fail2ban -y<br>
                            sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">5</span>配置SSH保护</h4>
                        <div class="code-block">
                            sudo nano /etc/fail2ban/jail.local<br><br>
                            <span class="command"># 在[sshd]部分添加</span><br>
                            enabled = true<br>
                            port = 2222<br>
                            maxretry = 3<br>
                            bantime = 1800
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4><span class="step-number">6</span>暴力破解模拟</h4>
                        <div class="code-block">
                            <span class="command"># 启动fail2ban</span><br>
                            sudo systemctl start fail2ban<br><br>
                            <span class="command"># 模拟多次失败登录</span><br>
                            for i in {1..5}; do<br>
                              ssh -p 2222 fakeuser@*************<br>
                            done<br><br>
                            <span class="command"># 检查封禁状态</span><br>
                            sudo fail2ban-client status sshd
                        </div>
                    </div>
                    
                    <div class="success">
                        <h4>✅ 任务三完成标准</h4>
                        <ul>
                            <li>端口扫描结果符合预期</li>
                            <li>密码认证完全禁用</li>
                            <li>fail2ban正确阻止暴力破解</li>
                            <li>安全日志记录完整</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 17: 故障排除与最佳实践 -->
        <div class="slide">
            <h1>故障排除指南与最佳实践</h1>
            <div class="two-column">
                <div>
                    <h3>🔧 常见问题排查</h3>
                    
                    <div class="config-section">
                        <h4>SSH连接问题</h4>
                        <div class="code-block">
                            <span class="command"># 检查SSH服务状态</span><br>
                            sudo systemctl status ssh<br><br>
                            <span class="command"># 查看SSH配置测试</span><br>
                            sudo sshd -t<br><br>
                            <span class="command"># 查看SSH监听端口</span><br>
                            sudo netstat -tlnp | grep sshd<br><br>
                            <span class="command"># 详细连接调试</span><br>
                            ssh -v -p 2222 user@*************
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>防火墙规则问题</h4>
                        <div class="code-block">
                            <span class="command"># 查看UFW状态</span><br>
                            sudo ufw status verbose<br><br>
                            <span class="command"># 查看iptables规则</span><br>
                            sudo iptables -L -n -v<br><br>
                            <span class="command"># 临时禁用防火墙测试</span><br>
                            sudo ufw disable
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>密钥认证问题</h4>
                        <div class="code-block">
                            <span class="command"># 检查authorized_keys权限</span><br>
                            ls -la ~/.ssh/<br>
                            chmod 700 ~/.ssh<br>
                            chmod 600 ~/.ssh/authorized_keys
                        </div>
                    </div>
                </div>
                <div>
                    <h3>⭐ 安全最佳实践</h3>
                    
                    <div class="highlight">
                        <h4>SSH安全基线</h4>
                        <ul>
                            <li>使用强密钥长度（≥2048位RSA或ED25519）</li>
                            <li>定期轮换SSH密钥对</li>
                            <li>启用双因子认证（如果可能）</li>
                            <li>使用非标准端口</li>
                            <li>限制登录用户和来源IP</li>
                            <li>配置会话超时</li>
                        </ul>
                    </div>
                    
                    <div class="highlight">
                        <h4>防火墙策略</h4>
                        <ul>
                            <li>采用默认拒绝策略</li>
                            <li>最小权限原则开放端口</li>
                            <li>使用地理位置过滤</li>
                            <li>启用连接速率限制</li>
                            <li>定期审计防火墙规则</li>
                        </ul>
                    </div>
                    
                    <div class="highlight">
                        <h4>监控与响应</h4>
                        <ul>
                            <li>配置实时日志监控</li>
                            <li>设置异常告警机制</li>
                            <li>建立安全事件响应流程</li>
                            <li>定期进行安全评估</li>
                        </ul>
                    </div>
                    
                    <div class="warning">
                        <h4>⚠️ 安全注意事项</h4>
                        <ul>
                            <li>始终保留应急访问方式</li>
                            <li>配置变更前充分测试</li>
                            <li>备份关键配置文件</li>
                            <li>文档化所有安全配置</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 18: 第四部分 - 核心要点回顾 -->
        <div class="slide">
            <h1>第四部分：核心要点回顾</h1>
            <div class="diagram">
                <h3>🔄 SSH安全加固完整流程</h3>
                <div class="network-diagram">
                    <pre>
    初始状态              加固过程              最终状态
┌──────────────┐      ┌──────────────┐      ┌──────────────┐
│ SSH默认配置  │ ──── │ 密钥对生成   │ ──── │ 密钥认证     │
│ 端口: 22     │      │ 配置修改     │      │ 端口: 2222   │
│ 密码认证     │      │ 服务重启     │      │ Root禁用     │
│ Root允许     │      └──────────────┘      │ 密码禁用     │
└──────────────┘                            └──────────────┘
        │                                           │
        ▼                                           ▼
┌──────────────┐      ┌──────────────┐      ┌──────────────┐
│ 防火墙默认   │ ──── │ UFW配置      │ ──── │ 精确控制     │
│ 全端口开放   │      │ 规则设置     │      │ IP白名单     │
│ 无访问控制   │      │ 策略应用     │      │ 端口限制     │
└──────────────┘      └──────────────┘      └──────────────┘
        │                                           │
        ▼                                           ▼
┌──────────────┐      ┌──────────────┐      ┌──────────────┐
│ 无入侵检测   │ ──── │ fail2ban配置 │ ──── │ 自动防护     │
│ 被动防御     │      │ 监控设置     │      │ 主动响应     │
│ 人工分析     │      │ 规则启用     │      │ 智能阻断     │
└──────────────┘      └──────────────┘      └──────────────┘
                    </pre>
                </div>
            </div>
            
            <div class="three-column">
                <div class="success">
                    <h4>🔐 SSH层面防护</h4>
                    <ul>
                        <li>强制密钥认证</li>
                        <li>禁用密码登录</li>
                        <li>非标准端口</li>
                        <li>Root权限控制</li>
                        <li>会话管理</li>
                    </ul>
                </div>
                <div class="success">
                    <h4>🛡️ 网络层面防护</h4>
                    <ul>
                        <li>默认拒绝策略</li>
                        <li>IP白名单控制</li>
                        <li>端口访问限制</li>
                        <li>连接状态跟踪</li>
                        <li>流量监控记录</li>
                    </ul>
                </div>
                <div class="success">
                    <h4>🚨 行为层面防护</h4>
                    <ul>
                        <li>失败尝试检测</li>
                        <li>自动封禁机制</li>
                        <li>异常行为分析</li>
                        <li>实时告警响应</li>
                        <li>安全事件记录</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 幻灯片 19: 知识拓展 - 高级安全主题 -->
        <div class="slide">
            <h1>知识拓展：企业级SSH安全架构</h1>
            <div class="two-column">
                <div>
                    <h3>🏗️ 高级安全架构</h3>
                    
                    <div class="config-section">
                        <h4>SSH隧道技术</h4>
                        <ul>
                            <li><strong>本地端口转发：</strong> ssh -L 8080:internal-server:80 user@gateway</li>
                            <li><strong>远程端口转发：</strong> ssh -R 9090:localhost:80 user@public-server</li>
                            <li><strong>动态代理：</strong> ssh -D 1080 user@proxy-server</li>
                        </ul>
                        
                        <div class="code-block">
                            <span class="command"># 建立安全隧道示例</span><br>
                            ssh -N -L 3306:db-server:3306 user@jump-server<br><br>
                            <span class="command"># 通过跳板机访问内网</span><br>
                            ssh -J user@jump-server user@internal-server
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>跳板机(Bastion Host)</h4>
                        <div class="network-diagram">
                            <pre>
Internet ──► [Bastion] ──► Internal Network
             Host         ┌─ Web Server
                         ├─ DB Server  
                         └─ App Server
                            </pre>
                        </div>
                        <ul>
                            <li>集中访问控制</li>
                            <li>审计日志记录</li>
                            <li>会话录制</li>
                            <li>权限精细化管理</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <h3>🔒 安全增强技术</h3>
                    
                    <div class="config-section">
                        <h4>多因子认证(MFA)</h4>
                        <div class="code-block">
                            <span class="command"># 安装Google Authenticator</span><br>
                            sudo apt install libpam-google-authenticator<br><br>
                            <span class="command"># 配置PAM模块</span><br>
                            echo "auth required pam_google_authenticator.so" >> /etc/pam.d/sshd<br><br>
                            <span class="command"># SSH配置启用</span><br>
                            ChallengeResponseAuthentication yes<br>
                            AuthenticationMethods publickey,keyboard-interactive
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>SELinux/AppArmor强制访问控制</h4>
                        <div class="code-block">
                            <span class="command"># SELinux SSH相关设置</span><br>
                            setsebool -P ssh_sysadm_login on<br>
                            semanage port -a -t ssh_port_t -p tcp 2222<br><br>
                            <span class="command"># AppArmor SSH配置</span><br>
                            sudo aa-enforce /usr/sbin/sshd
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>证书认证(Certificate-based)</h4>
                        <div class="code-block">
                            <span class="command"># 生成CA密钥</span><br>
                            ssh-keygen -t rsa -b 4096 -f ca_key<br><br>
                            <span class="command"># 签发用户证书</span><br>
                            ssh-keygen -s ca_key -I user1 -n user1 user1_key.pub
                        </div>
                    </div>
                    
                    <div class="highlight">
                        <h4>🔮 未来安全趋势</h4>
                        <ul>
                            <li><strong>零信任架构：</strong> 持续验证，永不信任</li>
                            <li><strong>量子安全算法：</strong> 抗量子计算破解</li>
                            <li><strong>AI威胁检测：</strong> 机器学习异常识别</li>
                            <li><strong>容器安全：</strong> 微服务架构防护</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 20: 实战经验分享 -->
        <div class="slide">
            <h1>实战经验分享与案例分析</h1>
            <div class="two-column">
                <div>
                    <h3>📊 真实攻击场景分析</h3>
                    
                    <div class="warning">
                        <h4>案例1：僵尸网络扫描</h4>
                        <div class="code-block">
                            <span class="command"># 日志中的典型攻击模式</span><br>
                            grep "Failed password" /var/log/auth.log | head -5
                        </div>
                        <div class="code-block output">
                            Feb 15 03:42:15 server sshd[1234]: Failed password for root from 203.0.113.10 port 54321
                            Feb 15 03:42:18 server sshd[1235]: Failed password for admin from 203.0.113.10 port 54322
                            Feb 15 03:42:21 server sshd[1236]: Failed password for user from 203.0.113.10 port 54323
                        </div>
                        <p><strong>防护效果：</strong> fail2ban自动封禁，UFW阻断后续连接</p>
                    </div>
                    
                    <div class="warning">
                        <h4>案例2：APT组织定向攻击</h4>
                        <ul>
                            <li>鱼叉式钓鱼获取凭据</li>
                            <li>横向移动寻找SSH服务</li>
                            <li>利用弱配置获取权限</li>
                            <li>建立后门维持访问</li>
                        </ul>
                        <p><strong>防护要点：</strong> 密钥认证 + 网络隔离 + 行为监控</p>
                    </div>
                    
                    <h3>🎯 企业部署策略</h3>
                    
                    <div class="config-section">
                        <h4>分级安全策略</h4>
                        <ul>
                            <li><strong>核心区域：</strong> 证书认证 + MFA + 跳板机</li>
                            <li><strong>管理区域：</strong> 密钥认证 + IP白名单</li>
                            <li><strong>开发区域：</strong> 密钥认证 + 防火墙</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <h3>⚡ 性能与安全平衡</h3>
                    
                    <div class="config-section">
                        <h4>连接性能优化</h4>
                        <div class="code-block">
                            <span class="command"># SSH客户端优化配置</span><br>
                            Host *<br>
                                Compression yes<br>
                                ServerAliveInterval 60<br>
                                ServerAliveCountMax 3<br>
                                ControlMaster auto<br>
                                ControlPath ~/.ssh/master-%r@%h:%p<br>
                                ControlPersist 10m
                        </div>
                    </div>
                    
                    <div class="config-section">
                        <h4>大规模部署自动化</h4>
                        <div class="code-block">
                            <span class="command"># Ansible批量配置示例</span><br>
                            - name: Configure SSH Security<br>
                              lineinfile:<br>
                                path: /etc/ssh/sshd_config<br>
                                regexp: '^PasswordAuthentication'<br>
                                line: 'PasswordAuthentication no'<br>
                              notify: restart ssh
                        </div>
                    </div>
                    
                    <div class="success">
                        <h4>✨ 管理效率提升</h4>
                        <ul>
                            <li><strong>配置管理：</strong> 版本控制 + 自动化部署</li>
                            <li><strong>密钥管理：</strong> 集中分发 + 自动轮换</li>
                            <li><strong>监控告警：</strong> 实时监控 + 智能分析</li>
                            <li><strong>合规审计：</strong> 日志收集 + 报告生成</li>
                        </ul>
                    </div>
                    
                    <div class="highlight">
                        <h4>💡 实施建议</h4>
                        <ul>
                            <li>分阶段渐进式实施</li>
                            <li>充分测试验证效果</li>
                            <li>建立应急响应机制</li>
                            <li>定期评估调优策略</li>
                            <li>持续安全意识培训</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 幻灯片 21-24: Q&A和总结 -->
        <div class="slide">
            <h1>课程总结与答疑</h1>
            <div style="text-align: center; padding: 50px;">
                <h2 style="color: #2a5298; margin-bottom: 40px;">🎓 恭喜完成SSH安全加固实战课程！</h2>
                
                <div class="three-column" style="margin: 50px 0;">
                    <div class="success">
                        <h3>📚 理论掌握</h3>
                        <ul style="text-align: left;">
                            <li>SSH协议安全机制</li>
                            <li>防火墙工作原理</li>
                            <li>协同防御思想</li>
                            <li>威胁分析方法</li>
                        </ul>
                    </div>
                    <div class="success">
                        <h3>🛠️ 技能获得</h3>
                        <ul style="text-align: left;">
                            <li>SSH服务加固配置</li>
                            <li>UFW防火墙管理</li>
                            <li>fail2ban部署使用</li>
                            <li>安全测试验证</li>
                        </ul>
                    </div>
                    <div class="success">
                        <h3>🔮 能力提升</h3>
                        <ul style="text-align: left;">
                            <li>安全威胁识别</li>
                            <li>防护策略设计</li>
                            <li>故障排除分析</li>
                            <li>持续改进意识</li>
                        </ul>
                    </div>
                </div>
                
                <div class="highlight" style="margin: 40px 0; font-size: 1.3em;">
                    <h3>🚀 下一步学习方向</h3>
                    <p>深入学习企业级安全架构、云安全防护、零信任网络、安全自动化运维等高级主题</p>
                </div>
                
                <div style="margin-top: 60px;">
                    <h2 style="color: #1e3c72;">💬 Q&A 自由提问时间</h2>
                    <p style="font-size: 1.2em; margin-top: 30px;">
                        欢迎提出关于SSH安全、防火墙配置、实验过程中遇到的问题，<br>
                        以及相关安全技术的任何疑问！
                    </p>
                </div>
            </div>
        </div>

        <!-- 感谢页面 -->
        <div class="slide">
            <div style="text-align: center; height: 100%; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                <h1 style="font-size: 3.5em; color: #1e3c72; margin-bottom: 50px;">感谢参与学习！</h1>
                
                <div style="font-size: 1.5em; margin-bottom: 60px; color: #2a5298;">
                    <p>SSH深度安全加固与防火墙协同防御实战</p>
                    <p>——— 构建企业级安全防护体系 ———</p>
                </div>
                
                <div class="highlight" style="font-size: 1.3em; margin-bottom: 50px;">
                    <p><strong>记住：</strong> 安全是一个持续的过程，不是一次性的配置。</p>
                    <p>定期评估、持续改进、保持学习是安全防护的关键。</p>
                </div>
                
                <div style="font-size: 1.2em; color: #666;">
                    <p>联系方式：<EMAIL></p>
                    <p
