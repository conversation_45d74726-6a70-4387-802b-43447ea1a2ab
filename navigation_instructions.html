<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿导航说明</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .instruction-card {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        h1 {
            text-align: center;
            color: #fff;
            margin-bottom: 40px;
            font-size: 2.5em;
        }
        
        h2 {
            color: #ffd700;
            border-bottom: 2px solid #ffd700;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .key-combo {
            background: rgba(0,0,0,0.3);
            padding: 8px 12px;
            border-radius: 5px;
            font-family: monospace;
            color: #ffd700;
            font-weight: bold;
        }
        
        .tip {
            background: rgba(255,165,0,0.2);
            border-left: 4px solid #ffa500;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        
        ul {
            list-style: none;
            padding: 0;
        }
        
        li {
            padding: 8px 0;
            display: flex;
            align-items: center;
        }
        
        li::before {
            content: "▶";
            color: #ffd700;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>🎯 SSH安全加固演示文稿导航指南</h1>
    
    <div class="instruction-card">
        <h2>⌨️ 键盘导航（最推荐）</h2>
        <ul>
            <li><span class="key-combo">→ 右箭头</span> 或 <span class="key-combo">空格键</span> 或 <span class="key-combo">Page Down</span> - 下一页</li>
            <li><span class="key-combo">← 左箭头</span> 或 <span class="key-combo">Page Up</span> - 上一页</li>
            <li><span class="key-combo">Home</span> - 跳转到第一页</li>
            <li><span class="key-combo">End</span> - 跳转到最后一页</li>
            <li><span class="key-combo">F11</span> - 全屏演示模式</li>
        </ul>
        
        <div class="tip">
            <strong>💡 专业技巧：</strong> 演示时建议使用空格键翻页，这是最自然的演讲节奏控制方式
        </div>
    </div>
    
    <div class="instruction-card">
        <h2>🖱️ 鼠标操作</h2>
        <ul>
            <li>点击页面底部的 <strong>"下一页 ▶"</strong> 按钮</li>
            <li>点击页面底部的 <strong>"◀ 上一页"</strong> 按钮</li>
            <li>鼠标滚轮向下滚动 - 下一页</li>
            <li>鼠标滚轮向上滚动 - 上一页</li>
        </ul>
        
        <div class="tip">
            <strong>⚠️ 注意：</strong> 滚轮导航需要一定幅度才会触发，避免意外翻页
        </div>
    </div>
    
    <div class="instruction-card">
        <h2>📱 触屏设备</h2>
        <ul>
            <li>左滑手势 - 下一页</li>
            <li>右滑手势 - 上一页</li>
        </ul>
    </div>
    
    <div class="instruction-card">
        <h2>📊 演示文稿信息</h2>
        <ul>
            <li><strong>总页数：</strong> 25张专业幻灯片</li>
            <li><strong>教学时长：</strong> 完整3小时课程</li>
            <li><strong>内容结构：</strong> 理论基础 → 讲师演示 → 学员实验 → 总结答疑</li>
            <li><strong>当前页码：</strong> 右上角实时显示进度</li>
        </ul>
    </div>
    
    <div class="instruction-card">
        <h2>🎨 高级功能</h2>
        <ul>
            <li><strong>智能边界：</strong> 第一页时左导航自动禁用，最后一页时右导航自动禁用</li>
            <li><strong>状态指示：</strong> 导航按钮根据位置动态变化</li>
            <li><strong>响应式设计：</strong> 适配不同屏幕尺寸</li>
            <li><strong>防误操作：</strong> 滚轮需要足够幅度才触发翻页</li>
        </ul>
        
        <div class="tip">
            <strong>🚀 隐藏功能：</strong> 按下F11进入全屏模式，获得最佳演示体验！
        </div>
    </div>
    
    <div class="instruction-card" style="text-align: center; background: rgba(255,215,0,0.1);">
        <h2>🎯 开始使用</h2>
        <p style="font-size: 1.2em;">现在您可以流畅地浏览这份SSH安全防护的专业教学材料了！</p>
        <p>建议教学时使用<span class="key-combo">空格键</span>控制节奏，配合<span class="key-combo">F11</span>全屏获得最佳效果</p>
    </div>
</body>
</html>
